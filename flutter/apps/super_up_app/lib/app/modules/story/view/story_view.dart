import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:story_view/controller/story_controller.dart';
import 'package:story_view/widgets/story_view.dart';
import 'package:super_up/app/core/api_service/story/story_api_service.dart';
import 'package:super_up/app/core/models/story/story_model.dart';
import 'package:super_up/app/core/models/story/story_reaction_model.dart';
import 'package:super_up/app/core/models/story/story_reply_model.dart';
import 'package:super_up/app/core/models/story/story_view_count_model.dart';
import 'package:super_up/app/modules/peer_profile/views/peer_profile_view.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../core/utils/enums.dart';

class StoryViewpage extends StatefulWidget {
  final UserStoryModel storyModel;
  final Function(UserStoryModel current)? onComplete;
  final Function()? onDelete;
  final Function(String storyId)? onStoryViewed;

  const StoryViewpage({
    super.key,
    required this.storyModel,
    required this.onComplete,
    required this.onDelete,
    this.onStoryViewed,
  });

  @override
  State<StoryViewpage> createState() => _StoryViewpageState();
}

class _StoryViewpageState extends State<StoryViewpage> {
  final controller = StoryController();

  final stories = <StoryItem>[];
  late StoryModel current = widget.storyModel.stories.first;
  final _api = GetIt.I.get<StoryApiService>();
  final _replyController = TextEditingController();

  // State for reactions and replies
  bool _isLiked = false;
  int _likesCount = 0;
  bool _isReacting = false;
  bool _isReplying = false;

  // State for view count
  int? _viewsCount;
  bool _isLoadingViewCount = false;

  @override
  void initState() {
    _parseStories();
    super.initState();
    // Mark the first story as viewed after the build is complete
    if (widget.storyModel.stories.isNotEmpty) {
      current = widget.storyModel.stories.first;
      // Use a delayed call to ensure the widget tree is fully built
      Future.delayed(Duration.zero, () {
        if (mounted) {
          unawaited(_setSeen(current.id));
          widget.onStoryViewed?.call(current.id);
          // Load view count for own stories
          if (widget.storyModel.userData.isMe) {
            _loadViewCount();
          }
        }
      });
    }
  }

  @override
  void dispose() {
    controller.dispose();
    _replyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          StoryView(
            onComplete: () {
              context.pop();
              widget.onComplete?.call(widget.storyModel);
            },
            onStoryShow: (storyItem, index) {
              int pos = stories.indexOf(storyItem);
              current = widget.storyModel.stories[pos];
              unawaited(_setSeen(current.id));
              // Notify the controller that this story was viewed using delayed call
              Future.delayed(Duration.zero, () {
                if (mounted) {
                  widget.onStoryViewed?.call(current.id);
                  // Load view count for own stories when story changes
                  if (widget.storyModel.userData.isMe) {
                    _loadViewCount();
                  }
                }
              });
            },
            storyItems: stories,
            controller: controller,
          ),
          // Custom caption overlay positioned higher up
          if (current.caption != null && current.caption!.isNotEmpty)
            Positioned(
              bottom: 120, // Position above the reply input area
              left: 20,
              right: 20,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  current.caption!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          Positioned(
            top: 25,
            left: 10,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                  ),
                ),
                InkWell(
                  onTap: () {
                    if (widget.storyModel.userData.isMe) return;
                    context.toPage(
                      PeerProfileView(peerId: widget.storyModel.userData.id),
                    );
                  },
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 10,
                      ),
                      VCircleAvatar(
                        vFileSource: VPlatformFile.fromUrl(
                          networkUrl: widget.storyModel.userData.userImage,
                        ),
                        radius: 20,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          widget.storyModel.userData.fullName.text.black
                              .color(Colors.white),
                          const SizedBox(
                            height: 3,
                          ),
                          format(
                            DateTime.parse(current.createdAt),
                            locale:
                                Localizations.localeOf(context).languageCode,
                          ).cap.color(Colors.white),
                          // Show view count for own stories
                          if (widget.storyModel.userData.isMe) ...[
                            const SizedBox(height: 3),
                            Row(
                              children: [
                                const Icon(
                                  Icons.visibility,
                                  color: Colors.white,
                                  size: 14,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _isLoadingViewCount
                                      ? "..."
                                      : "${_viewsCount ?? 0} views",
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (widget.storyModel.userData.isMe)
            Positioned(
              right: 3,
              top: 20,
              child: InkWell(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: PopupMenuButton<int>(
                    icon: Icon(
                      Icons.more_vert_sharp,
                      size: 30,
                      color: Colors.white,
                    ),
                    onSelected: (int result) async {
                      if (result == 1) {
                        final x = await VAppAlert.showAskYesNoDialog(
                          context: context,
                          title: S.of(context).delete,
                          content: S.of(context).areYouSure,
                        );
                        if (x == 1) {
                          await GetIt.I
                              .get<StoryApiService>()
                              .deleteStory(current.id);
                          VAppAlert.showSuccessSnackBar(
                              message: S.of(context).deleted, context: context);
                          if (widget.onDelete != null) {
                            widget.onDelete!();
                          }

                          context.pop();
                        }
                        // Perform delete action here
                        print('Delete button pressed');
                      }
                    },
                    itemBuilder: (BuildContext context) =>
                        <PopupMenuEntry<int>>[
                      PopupMenuItem<int>(
                        value: 1,
                        child: Text('Delete'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          // Reaction and Reply UI (only for other people's stories)
          if (!widget.storyModel.userData.isMe)
            Positioned(
              bottom: 20,
              left: 10,
              right: 10,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  children: [
                    // Heart reaction button
                    GestureDetector(
                      onTap: _reactToStory,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _isLiked ? Colors.red : Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: _isReacting
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Icon(
                                _isLiked
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: Colors.white,
                                size: 24,
                              ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Reply input field
                    Expanded(
                      child: TextField(
                        controller: _replyController,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          hintText:
                              "Reply to ${widget.storyModel.userData.fullName}...",
                          hintStyle:
                              TextStyle(color: Colors.white.withOpacity(0.7)),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                        ),
                        onSubmitted: (_) => _replyToStory(),
                      ),
                    ),
                    // Send reply button
                    GestureDetector(
                      onTap: _replyToStory,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: _isReplying
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Icon(
                                Icons.send,
                                color: Colors.white,
                                size: 24,
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future _setSeen(String id) async {
    vSafeApiCall(
      request: () async {
        await _api.setSeen(current.id);
      },
      onSuccess: (response) {},
    );
  }

  Future<void> _loadViewCount() async {
    if (!widget.storyModel.userData.isMe || _isLoadingViewCount) return;

    setState(() {
      _isLoadingViewCount = true;
    });

    await vSafeApiCall<StoryViewCountModel>(
      request: () async {
        return await _api.getStoryViewsCount(current.id);
      },
      onSuccess: (response) {
        setState(() {
          _viewsCount = response.viewsCount;
        });
      },
      onError: (exception, trace) {
        // Silently handle error - view count is not critical
      },
    );

    setState(() {
      _isLoadingViewCount = false;
    });
  }

  Future<void> _reactToStory() async {
    if (_isReacting || widget.storyModel.userData.isMe) return;

    setState(() {
      _isReacting = true;
    });

    await vSafeApiCall<StoryReactionModel>(
      request: () async {
        return await _api.reactToStory(current.id);
      },
      onSuccess: (response) {
        setState(() {
          _isLiked = response.liked;
          _likesCount = response.likesCount;
        });

        // Show feedback to user
        VAppAlert.showSuccessSnackBar(
          message: response.liked ? "❤️ Liked" : "Reaction removed",
          context: context,
        );
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: "Failed to react to story",
          context: context,
        );
      },
    );

    setState(() {
      _isReacting = false;
    });
  }

  Future<void> _replyToStory() async {
    if (_isReplying || widget.storyModel.userData.isMe) return;

    final text = _replyController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _isReplying = true;
    });

    await vSafeApiCall<StoryReplyResponse>(
      request: () async {
        return await _api.replyToStory(current.id, text);
      },
      onSuccess: (response) async {
        _replyController.clear();

        // Show success feedback
        VAppAlert.showSuccessSnackBar(
          message: "Reply sent",
          context: context,
        );

        // Navigate to chat with the story owner and create a story reply message
        await _navigateToStoryOwnerChat(text);
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: "Failed to send reply",
          context: context,
        );
      },
    );

    setState(() {
      _isReplying = false;
    });
  }

  Future<void> _navigateToStoryOwnerChat(String replyText) async {
    try {
      // Navigate to chat with the story owner
      await VChatController.I.roomApi.openChatWith(
        peerId: widget.storyModel.userData.id,
      );

      // Close the story view after navigating to chat
      if (mounted) {
        context.pop();
      }
    } catch (e) {
      VAppAlert.showErrorSnackBar(
        message: "Failed to open chat",
        context: context,
      );
    }
  }

  void _parseStories() {
    for (final story in widget.storyModel.stories) {
      if (story.storyType == StoryType.image) {
        stories.add(
          StoryItem.pageImage(
            url: VPlatformFile.fromUrl(networkUrl: story.att!['url']!)
                .fullNetworkUrl!,
            controller: controller,
            caption: null, // Remove built-in caption, we'll use custom overlay
            duration: const Duration(seconds: 7),
            imageFit: BoxFit.contain,
          ),
        );
        continue;
      }
      if (story.storyType == StoryType.video) {
        stories.add(
          StoryItem.pageVideo(
            VPlatformFile.fromUrl(networkUrl: story.att!['url']!)
                .fullNetworkUrl!,
            controller: controller,
            caption: null, // Remove built-in caption, we'll use custom overlay
            duration: const Duration(seconds: 15),
          ),
        );
        continue;
      }
      if (story.storyType == StoryType.text) {
        stories.add(
          StoryItem.text(
            title: story.content,
            duration: const Duration(seconds: 10),
            textStyle: TextStyle(
              color: Colors.white,
              fontSize: 35,
              fontStyle: story.fontType == StoryFontType.italic
                  ? FontStyle.italic
                  : null,
              textBaseline: TextBaseline.alphabetic,
              fontWeight:
                  story.fontType == StoryFontType.bold ? FontWeight.bold : null,
            ),
            backgroundColor: story.colorValue == null
                ? Colors.green
                : Color(story.colorValue!),
          ),
        );
        continue;
      }
    }
  }
}
