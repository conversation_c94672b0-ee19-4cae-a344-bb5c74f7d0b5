import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// Widget to display story reply messages in chat
class StoryReplyMessageWidget extends StatelessWidget {
  final bool isMeSender;
  final Map<String, dynamic> data;

  const StoryReplyMessageWidget({
    super.key,
    required this.isMeSender,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    // Debug: Print the received data structure
    print('StoryReplyMessageWidget received data: $data');
    print('StoryReplyMessageWidget data keys: ${data.keys.toList()}');
    print('StoryReplyMessageWidget data values: ${data.values.toList()}');

    // Extract story information from the data
    final storyType = data['storyType'] as String?;
    final storyContent = data['storyContent'] as String?;
    final storyCaption = data['storyCaption'] as String?;
    final replyText = data['replyText'] as String?;
    final backgroundColor = data['backgroundColor'] as String?;
    final textColor = data['textColor'] as String?;
    final storyAtt = data['storyAtt'] as Map<String, dynamic>?;

    // Debug: Print extracted values
    print('StoryReplyMessageWidget extracted values:');
    print('  storyType: $storyType');
    print('  storyContent: $storyContent');
    print('  storyCaption: $storyCaption');
    print('  replyText: $replyText');
    print('  backgroundColor: $backgroundColor');
    print('  textColor: $textColor');
    print('  storyAtt: $storyAtt');

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Story preview container
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isMeSender
                  ? Colors.black.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // "Replied to story" header
                Row(
                  children: [
                    Icon(
                      CupertinoIcons.reply,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Replied to story',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Story content preview
                _buildStoryPreview(context, storyType, storyContent,
                    storyCaption, backgroundColor, textColor, storyAtt),
              ],
            ),
          ),

          // Reply text
          if (replyText != null && replyText.isNotEmpty)
            Text(
              replyText,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStoryPreview(
    BuildContext context,
    String? storyType,
    String? storyContent,
    String? storyCaption,
    String? backgroundColor,
    String? textColor,
    Map<String, dynamic>? storyAtt,
  ) {
    if (storyType == 'text') {
      // Text story preview
      return Container(
        height: 80,
        width: double.infinity,
        decoration: BoxDecoration(
          color: backgroundColor != null
              ? _parseColor(backgroundColor)
              : Colors.blue,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            storyContent ?? '',
            style: TextStyle(
              color: textColor != null ? _parseColor(textColor) : Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      );
    } else if (storyType == 'image' || storyType == 'video') {
      // Media story preview
      return Container(
        height: 80,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            // Placeholder for media
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[400],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                storyType == 'video'
                    ? CupertinoIcons.play_circle
                    : CupertinoIcons.photo,
                color: Colors.white,
                size: 32,
              ),
            ),

            // Caption overlay if exists
            if (storyCaption != null && storyCaption.isNotEmpty)
              Positioned(
                bottom: 4,
                left: 4,
                right: 4,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    storyCaption,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    // Fallback for unknown story types
    return Container(
      height: 80,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          CupertinoIcons.doc_text,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  Color _parseColor(String colorString) {
    try {
      // Remove # if present and parse hex color
      final hexColor = colorString.replaceAll('#', '');
      return Color(int.parse('FF$hexColor', radix: 16));
    } catch (e) {
      // Return default color if parsing fails
      return Colors.blue;
    }
  }
}
