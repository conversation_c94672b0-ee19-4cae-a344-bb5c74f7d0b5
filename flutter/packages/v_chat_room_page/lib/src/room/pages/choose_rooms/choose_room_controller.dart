// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

/// Controller for selecting from a list of rooms. /// /// Extends [ValueNotifier] to allow [List ] to be listened to and updated. class ChooseRoomsController extends ValueNotifier<List > { /// The currently selected room ID. final String? currentId;
/// Create a new instance of [ChooseRoomsController]. /// /// Initializes an empty list of rooms and calls [_getRooms()] to fetch them. ChooseRoomsController(this.currentId) : super([]) { _getRooms(); } }
enum ChooseRoomsState { loading, loaded, error }

class ChooseRoomsController extends ValueNotifier<List<VRoom>> {
  final String? currentId;
  ChooseRoomsState _state = ChooseRoomsState.loading;
  String? _errorMessage;

  ChooseRoomsController(this.currentId) : super([]) {
    _getRooms();
  }

  void close() {}
  final maxForward = VChatController.I.vChatConfig.maxForward;

  ChooseRoomsState get state => _state;
  String? get errorMessage => _errorMessage;

  bool get isThereSelection =>
      value.firstWhereOrNull((e) => e.isSelected) == null;

  void onDone(BuildContext context) {
    final l = <String>[];
    for (var element in value) {
      if (element.isSelected) {
        l.add(element.id);
      }
    }
    Navigator.pop(context, l);
  }

  void _getRooms() async {
    _state = ChooseRoomsState.loading;
    _errorMessage = null;
    notifyListeners();

    await vSafeApiCall<List<VRoom>>(
      request: () async {
        // Add a small delay to ensure the app is fully loaded when accessed through share sheet
        await Future.delayed(const Duration(milliseconds: 500));

        try {
          // Check if user is authenticated by trying to access profile
          final accessToken =
              VAppPref.getHashedString(key: SStorageKeys.vAccessToken.name);
          if (accessToken == null) {
            throw Exception('User is not authenticated. Please login first.');
          }

          debugPrint(
              'ChooseRoomsController: Fetching rooms from server API...');
          debugPrint(
              'ChooseRoomsController: Base URL: ${VChatController.I.vChatConfig.baseUrl}');

          // Fetch from server API instead of local database
          final apiResponse =
              await VChatController.I.nativeApi.remote.room.getRooms(
            const VRoomsDto(limit: 120),
          );
          final vRooms = apiResponse.data;

          debugPrint(
              'ChooseRoomsController: Total rooms fetched: ${vRooms.length}');

          final filteredRooms = vRooms
              .where((e) => e.id != currentId && !e.roomType.isBroadcast)
              .toList();

          debugPrint(
              'ChooseRoomsController: Filtered rooms (excluding broadcasts and current): ${filteredRooms.length}');

          for (final room in filteredRooms) {
            debugPrint(
                'ChooseRoomsController: Room - ID: ${room.id}, Title: ${room.title}, Type: ${room.roomType}');
          }

          return filteredRooms;
        } catch (e) {
          // If there's an issue accessing the controller, throw a more descriptive error
          debugPrint('ChooseRoomsController: Error occurred: ${e.toString()}');
          throw Exception('Failed to load rooms: ${e.toString()}');
        }
      },
      onSuccess: (rooms) {
        debugPrint(
            'ChooseRoomsController: Successfully loaded ${rooms.length} rooms');
        value = rooms;
        _state = ChooseRoomsState.loaded;
        _errorMessage = null;
        notifyListeners();
      },
      onError: (exception, trace) {
        debugPrint('ChooseRoomsController: Error in vSafeApiCall: $exception');
        _state = ChooseRoomsState.error;
        _errorMessage = exception;
        value = [];
        notifyListeners();
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  void retryLoadRooms() {
    _getRooms();
  }

  int get selectedCount => value.where((e) => e.isSelected).length;

  bool isSelect(VRoom room) => room.isSelected == false;

  void onRoomItemPress(VRoom room, BuildContext context) {
    if (isSelect(room)) {
      if (selectedCount >= maxForward) {
        return;
      }
    }

    value.firstWhere((e) => e == room).toggleSelect();
    notifyListeners();
  }
}
