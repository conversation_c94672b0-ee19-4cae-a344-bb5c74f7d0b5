// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart' hide vSafeApiCall;

/// Controller for selecting from a list of rooms. /// /// Extends [ValueNotifier] to allow [List ] to be listened to and updated. class ChooseRoomsController extends ValueNotifier<List > { /// The currently selected room ID. final String? currentId;
/// Create a new instance of [ChooseRoomsController]. /// /// Initializes an empty list of rooms and calls [_getRooms()] to fetch them. ChooseRoomsController(this.currentId) : super([]) { _getRooms(); } }
enum ChooseRoomsState { loading, loaded, error }

class ChooseRoomsController extends ValueNotifier<List<VRoom>> {
  final String? currentId;
  ChooseRoomsState _state = ChooseRoomsState.loading;
  String? _errorMessage;

  ChooseRoomsController(this.currentId) : super([]) {
    _getRooms();
  }

  void close() {}
  final maxForward = VChatController.I.vChatConfig.maxForward;

  ChooseRoomsState get state => _state;
  String? get errorMessage => _errorMessage;

  bool get isThereSelection =>
      value.firstWhereOrNull((e) => e.isSelected) == null;

  void onDone(BuildContext context) {
    final l = <String>[];
    for (var element in value) {
      if (element.isSelected) {
        l.add(element.id);
      }
    }
    Navigator.pop(context, l);
  }

  void _getRooms() async {
    _state = ChooseRoomsState.loading;
    _errorMessage = null;
    notifyListeners();

    await vSafeApiCall<List<VRoom>>(
      request: () async {
        // Add a small delay to ensure the app is fully loaded when accessed through share sheet
        await Future.delayed(const Duration(milliseconds: 500));

        try {
          // Check if user is authenticated by trying to access profile
          final accessToken =
              VAppPref.getHashedString(key: SStorageKeys.vAccessToken.name);
          if (accessToken == null) {
            throw Exception('User is not authenticated. Please login first.');
          }

          final vRooms =
              await VChatController.I.nativeApi.local.room.getRooms(limit: 120);
          return vRooms
              .where((e) => e.id != currentId && !e.roomType.isBroadcast)
              .toList();
        } catch (e) {
          // If there's an issue accessing the controller, throw a more descriptive error
          throw Exception('Failed to load rooms: ${e.toString()}');
        }
      },
      onSuccess: (rooms) {
        value = rooms;
        _state = ChooseRoomsState.loaded;
        _errorMessage = null;
        notifyListeners();
      },
      onError: (exception, trace) {
        _state = ChooseRoomsState.error;
        _errorMessage = exception;
        value = [];
        notifyListeners();
        debugPrint('Error loading rooms: $exception');
      },
      ignoreTimeoutAndNoInternet: false,
    );
  }

  void retryLoadRooms() {
    _getRooms();
  }

  int get selectedCount => value.where((e) => e.isSelected).length;

  bool isSelect(VRoom room) => room.isSelected == false;

  void onRoomItemPress(VRoom room, BuildContext context) {
    if (isSelect(room)) {
      if (selectedCount >= maxForward) {
        return;
      }
    }

    value.firstWhere((e) => e == room).toggleSelect();
    notifyListeners();
  }
}
